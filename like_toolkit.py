import requests
import json

cookie = input("Intput cookie here: ")
likeID = input("Input like ID here: ")
url = "https://api-app-global.klingai.com/api/skit/feedback?caver=2"

payload = json.dumps({
  "skitId": f"{likeID}",
  "feedbackType": "star"
})
headers = {
  'accept': 'application/json, text/plain, */*',
  'accept-language': 'en',
  'content-type': 'application/json',
  'origin': 'https://app.klingai.com',
  'priority': 'u=1, i',
  'referer': 'https://app.klingai.com/',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-site',
  'time-zone': 'Asia/Saigon',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
  'Cookie': f'{cookie}'
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)
