#!/usr/bin/env python3
"""
MIT License API Module - Standalone License Checker
<PERSON><PERSON> thể sử dụng ở bất kỳ workspace nào

Usage:
    from license_api import LicenseAPI
    
    # Initialize
    license_api = LicenseAPI("http://your-server:5001", "your_tool_name")
    
    # Check license
    is_valid = license_api.check_license()
    
    # Get machine info
    machine_id = license_api.get_machine_id()
    formatted_id = license_api.format_machine_id(machine_id)
"""

import hashlib
import subprocess
import platform
import requests
import json
from datetime import datetime
from typing import Optional, Tuple, Dict, Any


class LicenseAPI:
    """Standalone License API for MIT Tools"""
    
    def __init__(self, server_url: str, tool_name: str):
        """
        Initialize License API
        
        Args:
            server_url: License server URL (e.g., "http://*************:5001")
            tool_name: Tool name for licensing (e.g., "mit_hailuo")
        """
        self.server_url = server_url.rstrip('/')
        self.tool_name = tool_name
        self.machine_id = self._get_machine_id()
        self.formatted_machine_id = self._format_machine_id(self.machine_id)
        self.license_key = f"{self.machine_id}_{self.tool_name}"
    
    def _get_machine_id(self) -> str:
        """Get unique machine identifier"""
        try:
            system = platform.system()
            
            if system == "Windows":
                # Try multiple Windows methods
                methods = [
                    ['wmic', 'csproduct', 'get', 'uuid'],
                    ['wmic', 'bios', 'get', 'serialnumber'],
                    ['wmic', 'baseboard', 'get', 'serialnumber']
                ]
                
                for method in methods:
                    try:
                        result = subprocess.run(method, capture_output=True, text=True, timeout=10)
                        if result.returncode == 0:
                            lines = result.stdout.strip().split('\n')
                            for line in lines:
                                line = line.strip()
                                if line and not line.lower().startswith(('uuid', 'serialnumber')):
                                    return line
                    except:
                        continue
                        
            elif system == "Darwin":  # macOS
                try:
                    result = subprocess.run(['system_profiler', 'SPHardwareDataType'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        for line in result.stdout.split('\n'):
                            if 'Hardware UUID' in line:
                                return line.split(':')[1].strip()
                except:
                    pass
                    
            elif system == "Linux":
                try:
                    # Try reading machine-id
                    with open('/etc/machine-id', 'r') as f:
                        return f.read().strip()
                except:
                    try:
                        # Fallback to DMI
                        result = subprocess.run(['dmidecode', '-s', 'system-uuid'], 
                                              capture_output=True, text=True, timeout=10)
                        if result.returncode == 0:
                            return result.stdout.strip()
                    except:
                        pass
            
            # Fallback: Generate from system info
            system_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
            return hashlib.md5(system_info.encode()).hexdigest()[:12].upper()
            
        except Exception as e:
            # Ultimate fallback
            fallback = f"FALLBACK-{platform.node()}"
            return hashlib.md5(fallback.encode()).hexdigest()[:12].upper()
    
    def _format_machine_id(self, machine_id: str) -> str:
        """Format machine ID for display (XXXXXX-XXXXXX)"""
        clean_id = ''.join(c for c in machine_id if c.isalnum()).upper()
        if len(clean_id) >= 12:
            return f"{clean_id[:6]}-{clean_id[6:12]}"
        else:
            # Pad if too short
            clean_id = clean_id.ljust(12, '0')
            return f"{clean_id[:6]}-{clean_id[6:12]}"
    
    def get_machine_id(self) -> str:
        """Get raw machine ID"""
        return self.machine_id
    
    def format_machine_id(self, machine_id: Optional[str] = None) -> str:
        """Get formatted machine ID"""
        if machine_id is None:
            return self.formatted_machine_id
        return self._format_machine_id(machine_id)
    
    def check_license(self, timeout: int = 10) -> bool:
        """
        Check if license is valid
        
        Args:
            timeout: Request timeout in seconds
            
        Returns:
            bool: True if license is valid, False otherwise
        """
        try:
            url = f"{self.server_url}/check-license"
            
            payload = {
                "license_key": self.license_key,
                "machine_id": self.machine_id,
                "tool_name": self.tool_name
            }
            
            response = requests.post(url, json=payload, timeout=timeout)
            
            if response.status_code == 200:
                data = response.json()
                return data.get("valid", False)
            else:
                return False
                
        except Exception as e:
            # Silent fail for network issues
            return False
    
    def get_license_info(self, timeout: int = 10) -> Dict[str, Any]:
        """
        Get detailed license information
        
        Args:
            timeout: Request timeout in seconds
            
        Returns:
            dict: License information or error details
        """
        try:
            url = f"{self.server_url}/check-license"
            
            payload = {
                "license_key": self.license_key,
                "machine_id": self.machine_id,
                "tool_name": self.tool_name
            }
            
            response = requests.post(url, json=payload, timeout=timeout)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    "valid": False,
                    "error": f"Server error: {response.status_code}",
                    "message": response.text
                }
                
        except requests.exceptions.Timeout:
            return {
                "valid": False,
                "error": "timeout",
                "message": "License server timeout"
            }
        except requests.exceptions.ConnectionError:
            return {
                "valid": False,
                "error": "connection_error", 
                "message": "Cannot connect to license server"
            }
        except Exception as e:
            return {
                "valid": False,
                "error": "unknown_error",
                "message": str(e)
            }
    
    def get_user_tools(self, timeout: int = 10) -> Dict[str, Any]:
        """
        Get list of tools for this machine
        
        Args:
            timeout: Request timeout in seconds
            
        Returns:
            dict: Tools information
        """
        try:
            url = f"{self.server_url}/get-tools"
            
            payload = {
                "machine_id": self.machine_id
            }
            
            response = requests.post(url, json=payload, timeout=timeout)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    "tools": [],
                    "error": f"Server error: {response.status_code}"
                }
                
        except Exception as e:
            return {
                "tools": [],
                "error": str(e)
            }
    
    def print_machine_info(self):
        """Print machine and tool information"""
        print("=" * 70)
        print("🖥️  THÔNG TIN MÁY TÍNH VÀ TOOL")
        print("=" * 70)
        print(f"📋 Mã máy: {self.formatted_machine_id}")
        print(f"🛠️ Tool hiện tại: {self.tool_name}")
        print(f"📦 Phiên bản: v1.0")
        print("=" * 70)
    
    def print_license_status(self):
        """Print detailed license status"""
        self.print_machine_info()
        
        print("📊 Đang kiểm tra danh sách tool của bạn...")
        tools_info = self.get_user_tools()
        
        if tools_info.get("tools"):
            print("✅ Danh sách tool được cấp phép:")
            for tool in tools_info["tools"]:
                print(f"   🔹 {tool}")
        else:
            print("❌ Bạn chưa có tool nào được cấp phép")
        
        print("=" * 70)
        print("💡 Để mua thêm tool, gửi mã máy cho admin kèm tên tool cần mua")
        print("=" * 70)


# Convenience functions for backward compatibility
def get_machine_id() -> str:
    """Get machine ID (standalone function)"""
    api = LicenseAPI("", "")
    return api.get_machine_id()


def format_machine_id(machine_id: str) -> str:
    """Format machine ID (standalone function)"""
    api = LicenseAPI("", "")
    return api.format_machine_id(machine_id)


# Example usage
if __name__ == "__main__":
    SERVER_URL = "http://*************:5001"
    TOOL_NAME = "mit_hailuo"
    
    # Initialize API
    license_api = LicenseAPI(SERVER_URL, TOOL_NAME)
    
    # Print machine info
    license_api.print_license_status()
    
    # Check license
    print("\n🔐 Checking license...")
    is_valid = license_api.check_license()
    
    if is_valid:
        print("✅ License is valid!")
    else:
        print("❌ License is invalid or expired!")
        
    # Get detailed info
    license_info = license_api.get_license_info()
    print(f"\n📋 License details: {license_info}")
