#!/usr/bin/env python3
"""
MIT Kling Follow - Secure Build Script
Builds executable without exposing server details
"""

import os
import subprocess
import sys
import shutil

def clean_build():
    """Clean previous build files"""
    folders = ["dist", "build", "__pycache__"]
    for folder in folders:
        if os.path.exists(folder):
            shutil.rmtree(folder)
            print(f"🗑️ Cleaned {folder}")

def build_exe():
    """Build executable with security measures"""
    print("🚀 Building MIT Kling Follow (Secure)...")
    
    # Clean previous builds
    clean_build()
    
    # PyInstaller command with security options
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed", 
        "--name=MIT_Kling_Follow",
        "--add-data=license_api.py;.",
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=requests",
        "--hidden-import=urllib3",
        "--hidden-import=certifi",
        "--hidden-import=base64",
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        "--exclude-module=numpy",
        "--strip",  # Strip debug symbols
        "--noupx",  # Disable UPX compression for better compatibility
        "kling_follow_gui.py"
    ]
    
    try:
        print("📦 Running PyInstaller with security options...")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Build successful!")
        
        # Check output
        exe_path = "dist/MIT_Kling_Follow.exe"
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📁 Output: {exe_path}")
            print(f"📏 Size: {size_mb:.1f} MB")
            
            # Create distribution package
            create_distribution()
            return True
        else:
            print("❌ Executable not found")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False

def create_distribution():
    """Create distribution package"""
    print("📦 Creating distribution package...")
    
    # Create README without server details
    readme_content = """# MIT Kling Follow v1.0.0

## 🚀 Professional Social Media Automation Tool

### 🔐 License System
- Each machine has a unique Machine ID
- License is tied to hardware fingerprint
- Secure server-based validation

### 📋 How to Get License

1. **Run the tool first time**
   - Tool will display your Machine ID
   - Example: `ABC123-DEF456`

2. **Contact for license**
   - 📱 Zalo/Phone: **************
   - 💬 Send your Machine ID
   - 💰 Payment as instructed

3. **Automatic activation**
   - License activates automatically after payment
   - Restart tool to use full features

### 🎯 Features

#### ⚡ Speed Optimized Mode
- 1 cookie = 1 task
- Best for high-speed operations

#### 🔄 Sequential Account Mode  
- Each account followed by ALL cookies
- Cookie 1→N follow Account 1, then Account 2
- Ensures complete coverage

### 📊 Recommended Settings
- **Threads**: 10-20 (avoid rate limits)
- **Target count**: Number of cookies to use
- **Batch processing**: Auto-optimized for memory

### ⚠️ Important Notes
- Internet connection required for license check
- Tool validates license on each startup
- Do not share license between machines
- Each machine has fixed Machine ID based on hardware

### 🆘 Support
- 📱 Zalo/Phone: **************
- 🐛 Bug reports: Send screenshot + Machine ID
- 💡 Feedback: Always welcome

---
**MIT Kling Follow** - Professional Social Media Automation
Copyright © 2024 - Contact: **********
"""
    
    with open("dist/README.txt", "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    # Create license info without server details
    license_info = """# MIT License System

## Machine ID
- Generated from hardware fingerprint
- Unique and fixed per machine
- Format: XXXXXX-XXXXXX

## License Activation
- Server-based validation system
- Automatic activation after payment
- Secure encrypted communication

## Getting License
- Contact: **********
- Send Machine ID for license
- Payment instructions will be provided

## Technical Notes
- License checked on startup
- Timeout: 10 seconds
- Fallback: Limited mode if server unavailable
- No server details exposed for security
"""
    
    with open("dist/LICENSE_INFO.txt", "w", encoding="utf-8") as f:
        f.write(license_info)
    
    print("✅ Distribution package created")

def main():
    print("=" * 60)
    print("🔐 MIT KLING FOLLOW - SECURE BUILD")
    print("=" * 60)
    
    if build_exe():
        print("\n" + "=" * 60)
        print("✅ SECURE BUILD COMPLETED!")
        print("=" * 60)
        print("📦 Files created:")
        print("   📁 dist/MIT_Kling_Follow.exe")
        print("   📄 dist/README.txt")
        print("   📄 dist/LICENSE_INFO.txt")
        print("")
        print("🔐 Security features:")
        print("   ✅ Server URL encoded and hidden")
        print("   ✅ No IP addresses in logs")
        print("   ✅ Error messages sanitized")
        print("   ✅ Distribution files clean")
        print("")
        print("🎯 Ready for distribution:")
        print("   📱 Users contact: **********")
        print("   🔑 License based on Machine ID")
        print("   🛡️ Server details protected")
        print("=" * 60)
    else:
        print("\n❌ Build failed!")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
