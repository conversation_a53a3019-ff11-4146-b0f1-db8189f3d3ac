#!/usr/bin/env python3
"""
Fix License Script - Reset và fix license issues
"""

import os
import sys
import hashlib
import platform
import requests
import json
from license_api import LicenseAPI

def get_stable_machine_id():
    """Tạo Machine ID ổn định dựa trên hardware"""
    try:
        # S<PERSON> dụng thông tin ổn định nhất
        system_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
        machine_id = hashlib.md5(system_info.encode()).hexdigest()[:12].upper()
        return machine_id
    except:
        return "FALLBACK123456"

def format_machine_id(machine_id):
    """Format machine ID"""
    clean_id = ''.join(c for c in machine_id if c.isalnum()).upper()
    if len(clean_id) >= 12:
        return f"{clean_id[:6]}-{clean_id[6:12]}"
    else:
        clean_id = clean_id.ljust(12, '0')
        return f"{clean_id[:6]}-{clean_id[6:12]}"

def test_license_connection():
    """Test kết nối đến license server"""
    server_url = "http://*************:5001"
    
    print("🔍 Testing license server connection...")
    try:
        response = requests.get(f"{server_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ License server is reachable")
            return True
        else:
            print(f"❌ Server returned: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

def check_license_on_server(machine_id, tool_name):
    """Kiểm tra license trên server"""
    server_url = "http://*************:5001"
    
    print(f"🔍 Checking license on server...")
    print(f"   Machine ID: {machine_id}")
    print(f"   Tool: {tool_name}")
    
    try:
        response = requests.get(
            f"{server_url}/check_license",
            params={
                'machine_id': machine_id,
                'tool_name': tool_name
            },
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Server response: {data}")
            return data
        else:
            print(f"❌ Server error: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return None

def register_license_key(machine_id, tool_name):
    """Đăng ký license key mới"""
    server_url = "http://*************:5001"
    license_key = f"{machine_id}_{tool_name}"
    
    print(f"📝 Registering license key: {license_key}")
    
    try:
        response = requests.post(
            f"{server_url}/register_license",
            json={
                'machine_id': machine_id,
                'tool_name': tool_name,
                'license_key': license_key
            },
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Registration successful: {data}")
            return True
        else:
            print(f"❌ Registration failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return False

def main():
    print("🔧 MIT LICENSE FIX TOOL")
    print("=" * 50)
    
    # 1. Get stable machine ID
    machine_id = get_stable_machine_id()
    formatted_id = format_machine_id(machine_id)
    tool_name = "mit_kling_follow"
    
    print(f"🖥️ Raw Machine ID: {machine_id}")
    print(f"🖥️ Formatted ID: {formatted_id}")
    print(f"🛠️ Tool: {tool_name}")
    print(f"🔑 License Key: {machine_id}_{tool_name}")
    print()
    
    # 2. Test server connection
    if not test_license_connection():
        print("❌ Cannot connect to license server. Please check network.")
        return
    print()
    
    # 3. Check current license status
    license_data = check_license_on_server(machine_id, tool_name)
    print()
    
    if license_data and license_data.get('valid'):
        print("✅ License is already valid!")
        print(f"   Status: {license_data}")
    else:
        print("❌ License not found or invalid")
        
        # Ask user if they want to register
        response = input("Do you want to register this machine? (y/n): ").lower()
        if response == 'y':
            if register_license_key(machine_id, tool_name):
                print("✅ License registered successfully!")
                print("📞 Contact ********** to activate your license")
            else:
                print("❌ Registration failed")
        else:
            print("📞 Contact ********** with your Machine ID to get license")
    
    print()
    print("🔄 Testing LicenseAPI...")
    try:
        license_api = LicenseAPI("http://*************:5001", tool_name)
        print(f"   API Machine ID: {license_api.formatted_machine_id}")
        print(f"   API License Key: {license_api.license_key}")
        
        is_valid = license_api.check_license()
        print(f"   License Valid: {is_valid}")
        
    except Exception as e:
        print(f"   ❌ API Error: {e}")
    
    print()
    print("🎯 SUMMARY:")
    print(f"   Machine ID: {formatted_id}")
    print(f"   License Key: {machine_id}_{tool_name}")
    print("   Contact: ********** for license activation")

if __name__ == "__main__":
    main()
