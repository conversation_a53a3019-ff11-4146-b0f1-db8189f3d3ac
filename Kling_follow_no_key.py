import sys
import json
import requests
import time
import random
import string
from requests.adapters import <PERSON><PERSON><PERSON><PERSON><PERSON>er
from urllib3.util.retry import Retry
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QPushButton, QLabel, QTextEdit, QLineEdit,
                             QFileDialog, QMessageBox, QProgressBar, QGroupBox,
                             QRadioButton, QButtonGroup, QSpinBox)
from PyQt6.QtCore import QThread, pyqtSignal, Qt, QThreadPool, QRunnable, QObject
from PyQt6.QtGui import QFont, QIcon
import time
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from datetime import datetime

# Import MIT License API - DISABLED
try:
    from license_api import LicenseAPI
    LICENSE_AVAILABLE = False  # Force disable license checking
except ImportError:
    LICENSE_AVAILABLE = False
    print("⚠️ License checking disabled. Running in demo mode.")

# MIT License Configuration - Encoded for security
import base64

def get_server_config():
    """Get server configuration with encoded URL"""
    # Multiple layer encoding for security
    layer1 = "YUhSMGNEb3ZMekUyTXk0Mk1TNDNNeTR4TVRjNk5UQXdNUT09"
    layer2 = base64.b64decode(layer1).decode('utf-8')
    server_url = base64.b64decode(layer2).decode('utf-8')

    return {
        "server_url": server_url,
        "tool_name": "mit_kling_follow",
        "version": "1.0.0"
    }

MIT_LICENSE_CONFIG = get_server_config()

def create_session():
    """Tạo session với connection pooling và retry strategy"""
    session = requests.Session()

    retry_strategy = Retry(
        total=2,
        backoff_factor=0.1,
        status_forcelist=[429, 500, 502, 503, 504],
    )

    adapter = HTTPAdapter(
        pool_connections=20,
        pool_maxsize=100,
        max_retries=retry_strategy
    )

    session.mount("http://", adapter)
    session.mount("https://", adapter)

    return session

class CookieResult:
    """Class để lưu kết quả cookie sau khi follow"""
    def __init__(self, cookie, cookie_num):
        self.cookie = cookie
        self.cookie_num = cookie_num
        self.is_live = None  # None = chưa test, True = live, False = die
        self.error_message = ""
        self.follow_attempts = 0
        self.follow_success = 0


class SingleFollowTask:
    """Class để xử lý 1 follow task"""
    def __init__(self, follow_id, cookie, cookie_num, total_cookies, session=None):
        self.follow_id = follow_id
        self.cookie = cookie
        self.cookie_num = cookie_num
        self.total_cookies = total_cookies
        self.result = None
        self.error = None
        self.is_cookie_live = None  # True = live, False = die (HTTP 401), None = unknown
        self.http_status = None
        self.session = session or create_session()

    def execute(self):
        """Thực hiện follow task"""
        try:
            n_number = random.randint(1, 999999999)
            url = f"https://api-app-global.klingai.com/api/user_follow/follow?__NS_hxfalcon=HUDR_sFnX-FFuAW5Vsf{n_number}DNK0XOP6snthhLc{n_number}vIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zb24e3VuXsMYojNEC2VAlN0_LMZHDhJK9nym8Jrx2cnp4iNdoYGECPbgEPyzfyJ_9LRRA2kT4Wi-fsgpPhcwt79cK7Hy9w4ZK644eMCLcihAJ9lm17QsTGX880s53arOcuxc1MeOcg-45Kx4xpyrjTOt_3Mkr_ImHFudSAZ-FJ7Y-DUacbgvXwTT7HftjkXvbvtUJeS55VYcnIluB1M4K-Uv1gmtWclhEjpbde9eMtPWx05vCI3m11uCAQ4dyGrbRAD2MclQ_kSuxM4i3sMrwRct-v8N3O4y1TZjuv0k84zX-0WgfvkgoTl-rJ8VA3qRUVEsglNKSEk-4zsj4yb75p6P72qGCI9g2M85afhcA3yOLpxu5Jqu9iIM0wab7w5YySD0-hZCpsJkqjS3BmUi1uqiM9-7-CFHDsZsspqotKT7H6RWq0vpeYZbzEZSKcmhzsW6xywLLMwA6qMtANJxzyg8XApiPFZwkP2FzStdTdEWNK2KC8KvwoUKWWicXFmHyk9Q1-v5zUAoXdswP7kYX8sRtP3eYGBzAQrbcZMUumOf_K-z0KVz9Xq6jH98SP2SKHDlUZfHUIaNxNBVjK2z7sPzqBut-_ndDpac7albRp-6CxTQ0rfsSI4dYdi4D6zWoMf7DUPjwrh4XP-e5nggZEyAnojq58hl0x-Y5$HE_001f87e2e0ad55091bd34a7ee5ef9b38584a4b4b4b4a89d3e7f158973df9301643d3d34ad01d7591301d75a34b&caver=2"
            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'en',
                'content-type': 'application/json',
                'origin': 'https://app.klingai.com',
                'priority': 'u=1, i',
                'referer': 'https://app.klingai.com/',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-site',
                'time-zone': 'Asia/Saigon',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                'Cookie': self.cookie,
            }

            payload = json.dumps({
                "dstId": int(self.follow_id),
                "followScene": "web_user_home",
                "followId": int(self.follow_id)
            })

            response = self.session.post(url, headers=headers, data=payload, timeout=5)
            self.http_status = response.status_code
            if response.status_code == 200:
                self.is_cookie_live = True  
                response_data = response.json()
                if response_data.get('result') == 1:
                    if response_data.get('status') == 200:
                        user_name = response_data.get('data', {}).get('userName', 'Unknown')
                        self.result = f"✓ [Cookie {self.cookie_num}/{self.total_cookies}] Follow thành công ID: {self.follow_id} ({user_name})"
                        return True
                    elif response_data.get('status') == 500:
                        # result=1, status=500 = follow thất bại, chuyển cookie khác
                        self.error = f"✗ [Cookie {self.cookie_num}/{self.total_cookies}] Follow thất bại ID: {self.follow_id} - Status 500 (chuyển cookie khác)"
                        return False
                    else:
                        self.error = f"✗ [Cookie {self.cookie_num}/{self.total_cookies}] Follow thất bại ID: {self.follow_id} - Status {response_data.get('status', 'Unknown')}"
                        return False
                else:
                    self.error = f"✗ [Cookie {self.cookie_num}/{self.total_cookies}] Follow thất bại ID: {self.follow_id} - {response_data.get('message', 'Unknown error')}"
                    return False
            elif response.status_code == 401:
                self.is_cookie_live = False 
                self.error = f"❌ [Cookie {self.cookie_num}/{self.total_cookies}] Cookie DIE - HTTP 401 (Unauthorized)"
                return False
            else:
                self.is_cookie_live = None  
                self.error = f"✗ [Cookie {self.cookie_num}/{self.total_cookies}] Follow thất bại ID: {self.follow_id} - HTTP {response.status_code}"
                return False

        except Exception as e:
            self.error = f"✗ [Cookie {self.cookie_num}/{self.total_cookies}] Lỗi khi follow ID: {self.follow_id} - {str(e)}"
            return False


class SingleLikeTask:
    """Class để xử lý 1 like task"""
    def __init__(self, like_id, cookie, cookie_num, total_cookies, session=None):
        self.like_id = like_id
        self.cookie = cookie
        self.cookie_num = cookie_num
        self.total_cookies = total_cookies
        self.result = None
        self.error = None
        self.is_cookie_live = None
        self.http_status = None
        self.session = session or create_session()

    def execute(self):
        """Thực hiện like task"""
        try:
            time.sleep(0.5 + (self.cookie_num % 3) * 0.2)
            url = "https://api-app-global.klingai.com/api/skit/feedback?__NS_hxfalcon=HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLcvIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4ZaG4e3VuXsMYojNEC2VAlN0_LMZHDhJK9nym8Jrx2cnp4iNdoYGECPbgEPyzfyJ_9LRRA2kT4Wi-fsgpPhcwt79cK7Hy9w4ZK644eMCLcihAJ9lm17QsTGX880s53arOcuxc1MeOcg-45Kx4xpyrjTOt_3Mkr_ImHFudSAZ-FJ7Y-DUacbgvXwTT7HftjkXvbvtUJeS55VYcnIluB1M4K-Uv1gmtWclhEjpbde9eMtPWx05vCI3m11uCAQ4dyGrbRAD2MclQ_kSuxM4i3sMrwRc9-v8N3O4y1TZjuv0k84zX-0WgfvkgoTjavJ8VA3o1UVEsglNKSEk-4zsj4yb75p6P72qGCI9g2M85afhcA3yOLpxu5Jqu9iIM0wab7w5YySD0-hZCpsJkqjS3BmUi1uqiM9-7-CFHDsZsspqotKT7H6RWq0vpeYZbzEZSKcmhzsW6xywLLMwA6qMtANJxzyg8XApiPFZwkCWFzStdTcEWNK2KC8KvwoUKWWicXFmHyn9Q1-v5zUAoXdswP7kYX8sRtP3eYGBzAQrbcZMUumOf_K-z0KVz9Xq6jH98SP2SKHDlUZfHUIaNxNBVjK2z7sPzqBut-_ndDpac7albRp-6CxTQ0rfsSI4dYdi4D6zWoMf7DUPjwrh4XP-e5nggZEyAnojq18hl0x-Y5$HE_2936aecbc98df5a52bfa636d64f99e46a96362626263acfaced874ba46832e03efe3fa63f9345cb819345c8a62&caver=2"

            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'en',
                'cache-control': 'no-cache',
                'content-type': 'application/json',
                'Cookie': self.cookie,
                'origin': 'https://app.klingai.com',
                'pragma': 'no-cache',
                'priority': 'u=1, i',
                'referer': 'https://app.klingai.com/',
                'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-site',
                'time-zone': 'Asia/Saigon',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
            }

            payload = json.dumps({
                "skitId": str(self.like_id),
                "feedbackType": "star"
            })

            response = self.session.post(url, headers=headers, data=payload, timeout=5)
            self.http_status = response.status_code

            if response.status_code == 200:
                self.is_cookie_live = True
                response_data = response.json()
                if response_data.get('result') == 1:
                    if response_data.get('status') == 200:
                        self.result = f"✓ [Cookie {self.cookie_num}/{self.total_cookies}] Like thành công ID: {self.like_id}"
                        return True
                    elif response_data.get('status') == 500:
                        # result=1, status=500 = like thất bại, chuyển cookie khác
                        self.error = f"✗ [Cookie {self.cookie_num}/{self.total_cookies}] Like thất bại ID: {self.like_id} - Status 500 (chuyển cookie khác)"
                        return False
                    else:
                        self.error = f"✗ [Cookie {self.cookie_num}/{self.total_cookies}] Like thất bại ID: {self.like_id} - Status {response_data.get('status', 'Unknown')}"
                        return False
                else:
                    self.error = f"✗ [Cookie {self.cookie_num}/{self.total_cookies}] Like thất bại ID: {self.like_id} - {response_data.get('message', 'Unknown error')}"
                    return False
            elif response.status_code == 401:
                self.is_cookie_live = False
                self.error = f"❌ [Cookie {self.cookie_num}/{self.total_cookies}] Cookie DIE - HTTP 401 (Unauthorized)"
                return False
            elif response.status_code == 501:
                self.is_cookie_live = True
                self.error = f"⚠️ [Cookie {self.cookie_num}/{self.total_cookies}] RATE LIMIT - HTTP 501 (访问太快，请稍候再试)"
                return False
            else:
                self.is_cookie_live = None
                self.error = f"✗ [Cookie {self.cookie_num}/{self.total_cookies}] Like thất bại ID: {self.like_id} - HTTP {response.status_code}"
                return False

        except requests.exceptions.Timeout:
            self.error = f"✗ [Cookie {self.cookie_num}/{self.total_cookies}] Timeout khi like ID: {self.like_id}"
            return False
        except requests.exceptions.RequestException as e:
            self.error = f"✗ [Cookie {self.cookie_num}/{self.total_cookies}] Request error khi like ID: {self.like_id} - {str(e)}"
            return False
        except Exception as e:
            self.error = f"✗ [Cookie {self.cookie_num}/{self.total_cookies}] Lỗi khi like ID: {self.like_id} - {str(e)}"
            return False


class FollowWorker(QThread):
    """Worker thread để thực hiện follow operations với đa luồng"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished = pyqtSignal(int, int, dict)

    def __init__(self, cookies, follow_ids, max_threads=5, target_count=None):
        super().__init__()
        self.cookies = cookies if isinstance(cookies, list) else [cookies]
        self.follow_ids = follow_ids
        self.success_count = 0
        self.target_count = target_count or len(follow_ids)
        self.total_count = 0  # Sẽ được cập nhật khi chạy

        self.current_cookie_index = 0
        self.max_threads = max_threads
        self.completed_tasks = 0
        self.lock = threading.Lock()
        self.is_running = True

        self.session = create_session()

        self.cookie_stats = {
            'live_cookies': [],
            'die_cookies': [],
            'unknown_cookies': [],
            'cookie_results': {}
        }

    def get_next_cookie(self):
        """Lấy cookie tiếp theo trong danh sách (round-robin)"""
        if not self.cookies:
            return None

        cookie = self.cookies[self.current_cookie_index]
        self.current_cookie_index = (self.current_cookie_index + 1) % len(self.cookies)
        return cookie

    def run(self):
        """Thực hiện follow operations với logic mới - đếm theo số follow thành công"""
        self.status_updated.emit(f"🚀 ĐA LUỒNG ĐANG HOẠT ĐỘNG: {self.max_threads} luồng song song")
        self.status_updated.emit(f"⚡ ThreadPoolExecutor khởi động với max_workers={self.max_threads}")
        self.status_updated.emit(f"🎯 Target: {self.target_count} follows thành công - Sử dụng {len(self.cookies)} cookies")

        with ThreadPoolExecutor(max_workers=self.max_threads) as executor:
            futures = []

            while self.success_count < self.target_count and self.is_running:
                # Thêm tasks mới nếu cần
                while len(futures) < self.max_threads and self.success_count < self.target_count and self.is_running:
                    cookie = self.get_next_cookie()
                    if not cookie:
                        break

                    if self.follow_ids:
                        follow_id = self.follow_ids[self.completed_tasks % len(self.follow_ids)]
                    else:
                        self.status_updated.emit("❌ Không có follow ID")
                        break

                    cookie_num = (self.completed_tasks % len(self.cookies)) + 1
                    task = SingleFollowTask(follow_id, cookie, cookie_num, len(self.cookies), self.session)
                    future = executor.submit(task.execute)
                    futures.append((future, task))

                # Xử lý các task hoàn thành
                completed_futures = []
                for future, task in futures:
                    if future.done():
                        completed_futures.append((future, task))

                for future, task in completed_futures:
                    futures.remove((future, task))

                    try:
                        success = future.result()

                        with self.lock:
                            self.completed_tasks += 1
                            self.total_count = self.completed_tasks
                            self.update_cookie_stats(task)

                            if success:
                                self.success_count += 1
                                if self.success_count % 10 == 0 or self.success_count <= 5:
                                    self.status_updated.emit(task.result)
                            else:
                                if task.is_cookie_live is False:
                                    self.status_updated.emit(task.error)
                                elif self.completed_tasks % 50 == 0:
                                    self.status_updated.emit(task.error)

                            # Cập nhật progress
                            current_progress = int(self.success_count / self.target_count * 100) if self.target_count > 0 else 0
                            self.progress_updated.emit(current_progress)

                    except Exception as e:
                        with self.lock:
                            self.completed_tasks += 1
                            self.total_count = self.completed_tasks
                            self.status_updated.emit(f"✗ [Cookie {task.cookie_num}] Lỗi không mong muốn: {str(e)}")

                # Kiểm tra điều kiện dừng
                if self.success_count >= self.target_count:
                    self.status_updated.emit(f"✅ Đã đạt target {self.success_count}/{self.target_count} follows thành công!")
                    break

                # Ngủ ngắn để tránh busy waiting
                time.sleep(0.01)

        self.progress_updated.emit(100)

        self.finished.emit(self.success_count, self.total_count, self.cookie_stats)

    def process_batch_results(self, results):
        """Xử lý batch results để giảm lock contention"""
        with self.lock:
            for task, success in results:
                self.completed_tasks += 1

                self.update_cookie_stats(task)

                if success:
                    self.success_count += 1
                    if self.completed_tasks % 50 == 0:
                        self.status_updated.emit(task.result)
                else:
                    if task.is_cookie_live is False: 
                        self.status_updated.emit(task.error)
                    elif self.completed_tasks % 100 == 0:
                        self.status_updated.emit(task.error)

    def update_cookie_stats(self, task):
        """Cập nhật thống kê cookie"""
        cookie = task.cookie

        if cookie not in self.cookie_stats['cookie_results']:
            self.cookie_stats['cookie_results'][cookie] = {
                'attempts': 0,
                'success': 0,
                'status': 'unknown',
                'cookie_num': task.cookie_num
            }

        self.cookie_stats['cookie_results'][cookie]['attempts'] += 1

        if task.result:
            self.cookie_stats['cookie_results'][cookie]['success'] += 1

        if task.is_cookie_live is True:
            self.cookie_stats['cookie_results'][cookie]['status'] = 'live'
            if cookie not in self.cookie_stats['live_cookies']:
                self.cookie_stats['live_cookies'].append(cookie)
        elif task.is_cookie_live is False:
            self.cookie_stats['cookie_results'][cookie]['status'] = 'die'
            if cookie not in self.cookie_stats['die_cookies']:
                self.cookie_stats['die_cookies'].append(cookie)
        else:
            if cookie not in self.cookie_stats['unknown_cookies']:
                self.cookie_stats['unknown_cookies'].append(cookie)

    def stop(self):
        """Dừng worker"""
        self.is_running = False


class CompleteFollowWorker(QThread):
    """Worker thread cho logic hoàn chỉnh: Mỗi cookie follow hết tất cả accounts"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished = pyqtSignal(int, int, dict)

    def __init__(self, cookies, account_ids, max_threads=5):
        super().__init__()
        self.cookies = cookies if isinstance(cookies, list) else [cookies]
        self.account_ids = account_ids if isinstance(account_ids, list) else [account_ids]
        self.max_threads = max_threads
        self.success_count = 0
        self.completed_tasks = 0
        self.total_tasks = len(self.cookies) * len(self.account_ids)
        self.lock = threading.Lock()
        self.is_running = True

        self.session = create_session()

        self.cookie_stats = {
            'live_cookies': [],
            'die_cookies': [],
            'unknown_cookies': [],
            'cookie_results': {} 
        }

    def run(self):
        """Thực hiện follow operations với logic tuần tự theo account"""
        self.status_updated.emit(f"🔄 LOGIC TUẦN TỰ THEO ACCOUNT: {len(self.cookies)} cookies × {len(self.account_ids)} accounts")
        self.status_updated.emit(f"🎯 Tổng tasks: {self.total_tasks}")
        self.status_updated.emit(f"🚀 Bắt đầu với {self.max_threads} luồng song song...")
        self.status_updated.emit(f"📋 Logic: Mỗi account được follow bởi TẤT CẢ cookies trước khi chuyển sang account tiếp theo")

        # Batch size cho cookies (để tránh tràn memory)
        cookie_batch_size = min(200, self.max_threads * 10)

        with ThreadPoolExecutor(max_workers=self.max_threads) as executor:
            # Xử lý từng ACCOUNT một cách tuần tự
            for account_index, account_id in enumerate(self.account_ids):
                if not self.is_running:
                    break

                self.status_updated.emit(f"🎯 Đang follow Account {account_index + 1}/{len(self.account_ids)}: {account_id}")
                self.status_updated.emit(f"🔄 Sử dụng {len(self.cookies)} cookies để follow account này...")

                # Xử lý TẤT CẢ cookies cho account này theo batch
                for batch_start in range(0, len(self.cookies), cookie_batch_size):
                    if not self.is_running:
                        break

                    batch_end = min(batch_start + cookie_batch_size, len(self.cookies))
                    batch_cookies = self.cookies[batch_start:batch_end]

                    self.status_updated.emit(f"📦 Batch cookies {batch_start + 1}-{batch_end} follow account {account_id}")

                    # Tạo tasks cho batch cookies này
                    batch_tasks = []
                    for cookie_index, cookie in enumerate(batch_cookies):
                        global_cookie_num = batch_start + cookie_index + 1
                        task = SingleFollowTask(account_id, cookie, global_cookie_num, len(self.cookies), self.session)
                        batch_tasks.append(task)

                    # Submit batch tasks
                    future_to_task = {executor.submit(task.execute): task for task in batch_tasks}

                    # Xử lý kết quả batch
                    for future in as_completed(future_to_task):
                        if not self.is_running:
                            break

                        task = future_to_task[future]
                        try:
                            success = future.result()

                            with self.lock:
                                self.completed_tasks += 1
                                self.update_cookie_stats(task)

                                if success:
                                    self.success_count += 1
                                    # Log mỗi 200 tasks để theo dõi
                                    if self.completed_tasks % 200 == 0:
                                        self.status_updated.emit(f"✅ Đã hoàn thành {self.completed_tasks}/{self.total_tasks} tasks")
                                else:
                                    # Chỉ log cookie die
                                    if task.is_cookie_live is False:
                                        self.status_updated.emit(task.error)

                                # Update progress
                                current_progress = int(self.completed_tasks / self.total_tasks * 100)
                                self.progress_updated.emit(current_progress)

                        except Exception as e:
                            with self.lock:
                                self.completed_tasks += 1
                                self.status_updated.emit(f"✗ [Cookie {task.cookie_num}] Lỗi: {str(e)}")

                    # Giải phóng memory sau mỗi batch
                    del batch_tasks, future_to_task

                    # Delay nhỏ giữa các batch để tránh rate limit
                    time.sleep(0.2)

                # Log hoàn thành account
                completed_accounts = account_index + 1
                self.status_updated.emit(f"✅ Hoàn thành Account {completed_accounts}/{len(self.account_ids)}: {account_id}")

                # Delay giữa các account
                if completed_accounts < len(self.account_ids):
                    time.sleep(0.5)

        # Hoàn thành
        self.progress_updated.emit(100)
        self.finished.emit(self.success_count, self.total_tasks, self.cookie_stats)

    def update_cookie_stats(self, task):
        """Cập nhật thống kê cookie"""
        cookie = task.cookie

        if cookie not in self.cookie_stats['cookie_results']:
            self.cookie_stats['cookie_results'][cookie] = {
                'attempts': 0,
                'success': 0,
                'status': 'unknown',
                'cookie_num': task.cookie_num
            }

        self.cookie_stats['cookie_results'][cookie]['attempts'] += 1

        if task.result:
            self.cookie_stats['cookie_results'][cookie]['success'] += 1

        if task.is_cookie_live is True:
            self.cookie_stats['cookie_results'][cookie]['status'] = 'live'
            if cookie not in self.cookie_stats['live_cookies']:
                self.cookie_stats['live_cookies'].append(cookie)
        elif task.is_cookie_live is False:
            self.cookie_stats['cookie_results'][cookie]['status'] = 'die'
            if cookie not in self.cookie_stats['die_cookies']:
                self.cookie_stats['die_cookies'].append(cookie)
        else:
            if cookie not in self.cookie_stats['unknown_cookies']:
                self.cookie_stats['unknown_cookies'].append(cookie)

    def stop(self):
        """Dừng worker"""
        self.is_running = False


class LikeWorker(QThread):
    """Worker thread để thực hiện like operations với đa luồng"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished = pyqtSignal(int, int, dict)

    def __init__(self, cookies, like_ids, max_threads=5, target_count=None):
        super().__init__()
        self.cookies = cookies if isinstance(cookies, list) else [cookies]
        self.like_ids = like_ids
        self.success_count = 0
        self.target_count = target_count or len(like_ids)
        self.total_count = 0  # Sẽ được cập nhật khi chạy

        self.current_cookie_index = 0
        self.max_threads = max_threads
        self.completed_tasks = 0
        self.lock = threading.Lock()
        self.is_running = True

        # Shared session cho tất cả tasks
        self.session = create_session()

        # Thống kê cookies
        self.cookie_stats = {
            'live_cookies': [],
            'die_cookies': [],
            'unknown_cookies': [],
            'cookie_results': {}  # cookie -> {attempts, success, status}
        }

    def get_next_cookie(self):
        """Lấy cookie tiếp theo theo round-robin"""
        if not self.cookies:
            return None

        cookie = self.cookies[self.current_cookie_index]
        self.current_cookie_index = (self.current_cookie_index + 1) % len(self.cookies)
        return cookie

    def run(self):
        """Thực hiện like operations với logic mới - đếm theo số like thành công"""
        self.status_updated.emit(f"🚀 ĐA LUỒNG ĐANG HOẠT ĐỘNG: {self.max_threads} luồng song song")
        self.status_updated.emit(f"⚡ ThreadPoolExecutor khởi động với max_workers={self.max_threads}")
        self.status_updated.emit(f"🎯 Target: {self.target_count} likes thành công - Sử dụng {len(self.cookies)} cookies")

        with ThreadPoolExecutor(max_workers=self.max_threads) as executor:
            futures = []

            while self.success_count < self.target_count and self.is_running:
                # Thêm tasks mới nếu cần
                while len(futures) < self.max_threads and self.success_count < self.target_count and self.is_running:
                    cookie = self.get_next_cookie()
                    if not cookie:
                        break

                    if self.like_ids:
                        like_id = self.like_ids[self.completed_tasks % len(self.like_ids)]
                    else:
                        self.status_updated.emit("❌ Không có like ID")
                        break

                    cookie_num = (self.completed_tasks % len(self.cookies)) + 1
                    task = SingleLikeTask(like_id, cookie, cookie_num, len(self.cookies), self.session)
                    future = executor.submit(task.execute)
                    futures.append((future, task))

                # Xử lý các task hoàn thành
                completed_futures = []
                for future, task in futures:
                    if future.done():
                        completed_futures.append((future, task))

                for future, task in completed_futures:
                    futures.remove((future, task))

                    try:
                        success = future.result()

                        with self.lock:
                            self.completed_tasks += 1
                            self.total_count = self.completed_tasks
                            self.update_cookie_stats(task)

                            if success:
                                self.success_count += 1
                                if self.success_count % 10 == 0 or self.success_count <= 5:
                                    self.status_updated.emit(task.result)
                            else:
                                if task.is_cookie_live is False:
                                    self.status_updated.emit(task.error)
                                elif self.completed_tasks % 50 == 0:
                                    self.status_updated.emit(task.error)

                            # Cập nhật progress
                            current_progress = int(self.success_count / self.target_count * 100) if self.target_count > 0 else 0
                            self.progress_updated.emit(current_progress)

                    except Exception as e:
                        with self.lock:
                            self.completed_tasks += 1
                            self.total_count = self.completed_tasks
                            self.status_updated.emit(f"✗ [Cookie {task.cookie_num}] Lỗi không mong muốn: {str(e)}")

                # Kiểm tra điều kiện dừng
                if self.success_count >= self.target_count:
                    self.status_updated.emit(f"✅ Đã đạt target {self.success_count}/{self.target_count} likes thành công!")
                    break

                # Ngủ ngắn để tránh busy waiting
                time.sleep(0.01)

        self.progress_updated.emit(100)

        # Hoàn thành
        self.finished.emit(self.success_count, self.total_count, self.cookie_stats)

    def process_batch_results(self, results):
        """Xử lý batch results để giảm lock contention"""
        with self.lock:
            for task, success in results:
                self.completed_tasks += 1

                # Thu thập thống kê cookie
                self.update_cookie_stats(task)

                if success:
                    self.success_count += 1
                    # Chỉ emit log cho một số successful tasks để giảm spam
                    if self.completed_tasks % 50 == 0:  # Mỗi 50 tasks
                        self.status_updated.emit(task.result)
                else:
                    # Emit error nhưng giới hạn để tránh spam
                    if task.is_cookie_live is False:  # Chỉ log cookie die
                        self.status_updated.emit(task.error)
                    elif self.completed_tasks % 100 == 0:  # Log error khác mỗi 100 tasks
                        self.status_updated.emit(task.error)

    def update_cookie_stats(self, task):
        """Cập nhật thống kê cookie"""
        cookie = task.cookie

        # Khởi tạo stats cho cookie nếu chưa có
        if cookie not in self.cookie_stats['cookie_results']:
            self.cookie_stats['cookie_results'][cookie] = {
                'attempts': 0,
                'success': 0,
                'status': 'unknown',
                'cookie_num': task.cookie_num
            }

        # Cập nhật attempts
        self.cookie_stats['cookie_results'][cookie]['attempts'] += 1

        # Cập nhật success
        if task.result:  # Nếu có result thì là success
            self.cookie_stats['cookie_results'][cookie]['success'] += 1

        # Cập nhật trạng thái cookie
        if task.is_cookie_live is True:
            self.cookie_stats['cookie_results'][cookie]['status'] = 'live'
            if cookie not in self.cookie_stats['live_cookies']:
                self.cookie_stats['live_cookies'].append(cookie)
        elif task.is_cookie_live is False:
            self.cookie_stats['cookie_results'][cookie]['status'] = 'die'
            if cookie not in self.cookie_stats['die_cookies']:
                self.cookie_stats['die_cookies'].append(cookie)
        else:
            if cookie not in self.cookie_stats['unknown_cookies']:
                self.cookie_stats['unknown_cookies'].append(cookie)

    def stop(self):
        """Dừng worker"""
        self.is_running = False


class CompleteLikeWorker(QThread):
    """Worker thread cho logic hoàn chỉnh: Mỗi cookie like hết tất cả videos"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished = pyqtSignal(int, int, dict)  # success_count, total_count, cookie_stats

    def __init__(self, cookies, video_ids, max_threads=5):
        super().__init__()
        self.cookies = cookies if isinstance(cookies, list) else [cookies]
        self.video_ids = video_ids if isinstance(video_ids, list) else [video_ids]
        self.max_threads = max_threads
        self.success_count = 0
        self.completed_tasks = 0
        self.total_tasks = len(self.cookies) * len(self.video_ids)
        self.lock = threading.Lock()
        self.is_running = True

        # Shared session cho tất cả tasks
        self.session = create_session()

        # Thống kê cookies
        self.cookie_stats = {
            'live_cookies': [],
            'die_cookies': [],
            'unknown_cookies': [],
            'cookie_results': {}  # cookie -> {attempts, success, status}
        }

    def run(self):
        """Thực hiện like operations với logic tuần tự theo video"""
        self.status_updated.emit(f"🔄 LOGIC TUẦN TỰ THEO VIDEO: {len(self.cookies)} cookies × {len(self.video_ids)} videos")
        self.status_updated.emit(f"🎯 Tổng tasks: {self.total_tasks}")
        self.status_updated.emit(f"🚀 Bắt đầu với {self.max_threads} luồng song song...")
        self.status_updated.emit(f"📋 Logic: Mỗi video được like bởi TẤT CẢ cookies trước khi chuyển sang video tiếp theo")

        # Batch size cho cookies (để tránh tràn memory)
        cookie_batch_size = min(200, self.max_threads * 10)

        with ThreadPoolExecutor(max_workers=self.max_threads) as executor:
            # Xử lý từng VIDEO một cách tuần tự
            for video_index, video_id in enumerate(self.video_ids):
                if not self.is_running:
                    break

                self.status_updated.emit(f"🎯 Đang like Video {video_index + 1}/{len(self.video_ids)}: {video_id}")
                self.status_updated.emit(f"🔄 Sử dụng {len(self.cookies)} cookies để like video này...")

                # Xử lý TẤT CẢ cookies cho video này theo batch
                for batch_start in range(0, len(self.cookies), cookie_batch_size):
                    if not self.is_running:
                        break

                    batch_end = min(batch_start + cookie_batch_size, len(self.cookies))
                    batch_cookies = self.cookies[batch_start:batch_end]

                    self.status_updated.emit(f"📦 Batch cookies {batch_start + 1}-{batch_end} like video {video_id}")

                    # Tạo tasks cho batch cookies này
                    batch_tasks = []
                    for cookie_index, cookie in enumerate(batch_cookies):
                        global_cookie_num = batch_start + cookie_index + 1
                        task = SingleLikeTask(video_id, cookie, global_cookie_num, len(self.cookies), self.session)
                        batch_tasks.append(task)

                    # Submit batch tasks
                    future_to_task = {executor.submit(task.execute): task for task in batch_tasks}

                    # Xử lý kết quả batch
                    for future in as_completed(future_to_task):
                        if not self.is_running:
                            break

                        task = future_to_task[future]
                        try:
                            success = future.result()

                            with self.lock:
                                self.completed_tasks += 1
                                self.update_cookie_stats(task)

                                if success:
                                    self.success_count += 1
                                    # Log mỗi 200 tasks để theo dõi
                                    if self.completed_tasks % 200 == 0:
                                        self.status_updated.emit(f"✅ Đã hoàn thành {self.completed_tasks}/{self.total_tasks} tasks")
                                else:
                                    # Chỉ log cookie die
                                    if task.is_cookie_live is False:
                                        self.status_updated.emit(task.error)

                                # Update progress
                                current_progress = int(self.completed_tasks / self.total_tasks * 100)
                                self.progress_updated.emit(current_progress)

                        except Exception as e:
                            with self.lock:
                                self.completed_tasks += 1
                                self.status_updated.emit(f"✗ [Cookie {task.cookie_num}] Lỗi: {str(e)}")

                    # Giải phóng memory sau mỗi batch
                    del batch_tasks, future_to_task

                    # Delay nhỏ giữa các batch để tránh rate limit
                    time.sleep(0.2)

                # Log hoàn thành video
                completed_videos = video_index + 1
                self.status_updated.emit(f"✅ Hoàn thành Video {completed_videos}/{len(self.video_ids)}: {video_id}")

                # Delay giữa các video
                if completed_videos < len(self.video_ids):
                    time.sleep(0.5)

        # Hoàn thành
        self.progress_updated.emit(100)
        self.finished.emit(self.success_count, self.total_tasks, self.cookie_stats)

    def update_cookie_stats(self, task):
        """Cập nhật thống kê cookie"""
        cookie = task.cookie

        # Khởi tạo stats cho cookie nếu chưa có
        if cookie not in self.cookie_stats['cookie_results']:
            self.cookie_stats['cookie_results'][cookie] = {
                'attempts': 0,
                'success': 0,
                'status': 'unknown',
                'cookie_num': task.cookie_num
            }

        # Cập nhật attempts
        self.cookie_stats['cookie_results'][cookie]['attempts'] += 1

        # Cập nhật success
        if task.result:  # Nếu có result thì là success
            self.cookie_stats['cookie_results'][cookie]['success'] += 1

        # Cập nhật trạng thái cookie
        if task.is_cookie_live is True:
            self.cookie_stats['cookie_results'][cookie]['status'] = 'live'
            if cookie not in self.cookie_stats['live_cookies']:
                self.cookie_stats['live_cookies'].append(cookie)
        elif task.is_cookie_live is False:
            self.cookie_stats['cookie_results'][cookie]['status'] = 'die'
            if cookie not in self.cookie_stats['die_cookies']:
                self.cookie_stats['die_cookies'].append(cookie)
        else:
            if cookie not in self.cookie_stats['unknown_cookies']:
                self.cookie_stats['unknown_cookies'].append(cookie)

    def stop(self):
        """Dừng worker"""
        self.is_running = False


class KlingFollowGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.cookies = []  # Danh sách tất cả cookies
        self.cookie_stats = None  # Thống kê cookies sau khi follow/like
        self.follow_worker = None
        self.like_worker = None
        self.current_mode = "follow"  # "follow" hoặc "like"
        self.auto_update_enabled = True  # Tự động cập nhật cookie live
        self.cookie_file_path = None  # Đường dẫn file cookie đã import

        # Initialize MIT License - DISABLED
        self.license_api = None
        self.is_licensed = True  # Force enable all features
        self.init_license()

        # Initialize UI
        self.init_ui()

        # Check license after UI is ready - DISABLED
        self.check_license_status()

    def init_license(self):
        """Initialize MIT License API - DISABLED"""
        print("⚠️ License checking disabled - Running in full demo mode")
        print("✅ All features unlocked without license validation")
        self.license_api = None

    def check_license_status(self):
        """Check license status and show appropriate message - DISABLED"""
        self.show_demo_mode_message()

    def show_demo_mode_message(self):
        """Show demo mode message"""
        self.log_message("✅ License checking disabled - All features unlocked")
        self.setWindowTitle("MIT Kling Follow ********** - Full Version")
        if hasattr(self, 'license_status_label'):
            self.license_status_label.setText("✅ Full Version - All features unlocked")
            self.license_status_label.setStyleSheet("color: green; font-weight: bold;")

    def show_license_error(self):
        """Show license error and machine info - DISABLED"""
        # License checking disabled - this function does nothing
        pass

    def show_license_required_message(self):
        """Show license required message when trying to use features - DISABLED"""
        # License checking disabled - this function does nothing
        pass

    def show_license_details(self):
        """Show detailed license information"""
        QMessageBox.information(self, "License Info",
                              "✅ Full Version\n\nLicense checking is disabled.\nAll features are unlocked and available.")
        return



    def init_ui(self):
        """Khởi tạo giao diện người dùng"""
        self.setWindowTitle("MIT Kling Follow **********")
        self.setGeometry(100, 100, 600, 700)
        
        # Widget chính
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout chính
        main_layout = QVBoxLayout(central_widget)
        
        # Title
        title_label = QLabel("🚀 MIT Kling Follow **********")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)

        # License info section
        license_group = QGroupBox("🔐 MIT License Info")
        license_layout = QHBoxLayout(license_group)

        self.license_status_label = QLabel("Checking license...")
        license_layout.addWidget(self.license_status_label)

        self.license_info_button = QPushButton("📋 License Details")
        self.license_info_button.clicked.connect(self.show_license_details)
        self.license_info_button.setMaximumWidth(150)
        license_layout.addWidget(self.license_info_button)

        main_layout.addWidget(license_group)
        
        # Cookie section
        cookie_group = QGroupBox("📁 Import Cookie")
        cookie_layout = QVBoxLayout(cookie_group)
        
        cookie_button_layout = QHBoxLayout()
        self.cookie_button = QPushButton("Chọn file cookie (.txt)")
        self.cookie_button.clicked.connect(self.import_cookie)
        cookie_button_layout.addWidget(self.cookie_button)
        
        self.cookie_status = QLabel("Chưa import cookie")
        self.cookie_status.setStyleSheet("color: red;")
        cookie_button_layout.addWidget(self.cookie_status)
        
        cookie_layout.addLayout(cookie_button_layout)

        # Auto update cookie toggle section
        auto_update_layout = QHBoxLayout()

        self.auto_update_checkbox = QPushButton("🔄 Tự động cập nhật cookie live")
        self.auto_update_checkbox.setCheckable(True)
        self.auto_update_checkbox.setChecked(True)  # Mặc định bật
        self.auto_update_checkbox.clicked.connect(self.toggle_auto_update)
        self.auto_update_checkbox.setToolTip("Tự động ghi đè file cookie đã import bằng danh sách cookies live sau khi hoàn thành")
        self.auto_update_checkbox.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 6px 12px;
                font-size: 11px;
                font-weight: bold;
                border-radius: 4px;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:checked {
                background-color: #4CAF50;
            }
            QPushButton:!checked {
                background-color: #f44336;
            }
            QPushButton:!checked:hover {
                background-color: #da190b;
            }
        """)

        self.auto_update_status = QLabel("✅ BẬT")
        self.auto_update_status.setStyleSheet("color: green; font-weight: bold; font-size: 11px;")

        auto_update_layout.addWidget(self.auto_update_checkbox)
        auto_update_layout.addWidget(self.auto_update_status)
        auto_update_layout.addStretch()

        cookie_layout.addLayout(auto_update_layout)

        # Export cookies section
        export_layout = QHBoxLayout()
        self.export_live_button = QPushButton("� Xuất Cookies Live")
        self.export_live_button.clicked.connect(self.export_live_cookies)
        self.export_live_button.setEnabled(False)
        self.export_live_button.setToolTip("Xuất cookies live sau khi follow (tự động phân loại dựa trên kết quả follow)")
        self.export_live_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px;
                font-size: 12px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)

        export_layout.addWidget(self.export_live_button)

        self.export_results_button = QPushButton("📊 Xuất Kết Quả")
        self.export_results_button.clicked.connect(self.export_results)
        self.export_results_button.setEnabled(False)  # Disable ban đầu
        self.export_results_button.setToolTip("Xuất báo cáo chi tiết kết quả follow/like")
        self.export_results_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px;
                font-size: 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)

        export_layout.addWidget(self.export_results_button)
        export_layout.addStretch()
        cookie_layout.addLayout(export_layout)

        main_layout.addWidget(cookie_group)
        
        # Mode selection section
        mode_group = QGroupBox("🎯 Chế độ hoạt động")
        mode_group_layout = QVBoxLayout(mode_group)

        # Radio buttons cho chế độ Follow/Like
        operation_layout = QHBoxLayout()
        self.operation_group = QButtonGroup()

        self.follow_operation = QRadioButton("👥 Follow Users")
        self.follow_operation.setChecked(True)
        self.like_operation = QRadioButton("❤️ Like Videos")

        self.operation_group.addButton(self.follow_operation)
        self.operation_group.addButton(self.like_operation)

        self.follow_operation.toggled.connect(self.on_operation_changed)

        operation_layout.addWidget(self.follow_operation)
        operation_layout.addWidget(self.like_operation)
        mode_group_layout.addLayout(operation_layout)

        main_layout.addWidget(mode_group)

        # Follow/Like ID section
        self.id_group = QGroupBox("👥 Follow IDs")
        self.id_layout = QVBoxLayout(self.id_group)
        
        # Radio buttons cho chế độ nhập
        mode_layout = QHBoxLayout()
        self.single_mode = QRadioButton("📍 Nhập 1 ID")
        self.multiple_mode = QRadioButton("📋 Nhập nhiều IDs")
        self.single_mode.setChecked(True)

        self.mode_group = QButtonGroup()
        self.mode_group.addButton(self.single_mode)
        self.mode_group.addButton(self.multiple_mode)

        # Connect signal để xử lý thay đổi chế độ
        self.single_mode.toggled.connect(self.on_mode_changed)

        mode_layout.addWidget(self.single_mode)
        mode_layout.addWidget(self.multiple_mode)
        self.id_layout.addLayout(mode_layout)
        
        # Input cho single ID
        self.single_input = QLineEdit()
        self.single_input.setPlaceholderText("Nhập Follow ID (ví dụ: 36709052)")
        self.id_layout.addWidget(self.single_input)

        # Input cho multiple IDs
        self.multiple_input = QTextEdit()
        self.multiple_input.setPlaceholderText("Nhập danh sách Follow IDs, mỗi ID một dòng:\n36709052\n12345678\n87654321")
        self.multiple_input.setMaximumHeight(140)
        self.multiple_input.hide()
        self.id_layout.addWidget(self.multiple_input)

        # Logic mode selection
        logic_group = QGroupBox("🎯 Chế độ Logic")
        logic_layout = QVBoxLayout(logic_group)

        self.logic_mode_group = QButtonGroup()

        # Logic mode 1: Tối ưu tốc độ (1 cookie = 1 task)
        self.speed_mode = QRadioButton("⚡ Tối ưu tốc độ: 1 cookie = 1 task")
        self.speed_mode.setChecked(True)
        self.speed_mode.setToolTip(
            "LOGIC TỐI ƯU TỐC ĐỘ:\n"
            "• Target 1200 → sử dụng 1200 cookies đầu tiên\n"
            "• 1 cookie = 1 task = tốc độ tối ưu\n"
            "• Không cần kiểm tra live/die\n"
            "• IDs được phân phối round-robin qua cookies"
        )

        # Logic mode 2: Tuần tự theo account - mỗi account được follow bởi TẤT CẢ cookies
        self.complete_mode = QRadioButton("🔄 Tuần tự theo Account: Mỗi account được follow bởi TẤT CẢ cookies")
        self.complete_mode.setToolTip(
            "LOGIC TUẦN TỰ THEO ACCOUNT:\n"
            "• N cookies, M accounts\n"
            "• Account 1: Cookie 1→N follow account 1\n"
            "• Account 2: Cookie 1→N follow account 2\n"
            "• ...\n"
            "• Target 1200 → chỉ dùng đúng 1200 cookies\n"
            "• Tổng tasks = N cookies × M accounts\n\n"
            "VÍ DỤ: 1000 cookies × 10 accounts, 50 luồng:\n"
            "• 50 luồng chạy cookie 1→1000 follow account 1\n"
            "• Xong account 1 → chuyển sang account 2\n"
            "• 50 luồng chạy cookie 1→1000 follow account 2\n"
            "• Cứ như vậy cho đến hết 10 accounts"
        )

        self.logic_mode_group.addButton(self.speed_mode)
        self.logic_mode_group.addButton(self.complete_mode)

        logic_layout.addWidget(self.speed_mode)
        logic_layout.addWidget(self.complete_mode)
        self.id_layout.addWidget(logic_group)

        # Cài đặt đa luồng - Hiển thị rõ ràng hơn
        thread_layout = QHBoxLayout()
        thread_label = QLabel("🚀 Số luồng song song:")
        thread_label.setStyleSheet("font-weight: bold; color: #2196F3; font-size: 12px;")
        self.thread_spinbox = QSpinBox()
        self.thread_spinbox.setMinimum(1)
        self.thread_spinbox.setMaximum(100)
        self.thread_spinbox.setValue(10)  # Tăng lên 10 luồng để tối ưu tốc độ
        self.thread_spinbox.setStyleSheet("QSpinBox { font-weight: bold; color: #2196F3; font-size: 12px; }")
        self.thread_spinbox.setToolTip("Số luồng chạy song song (1-100). Khuyến nghị: 10-20 luồng cho Follow, 3-5 luồng cho Like")

        # Thêm label hiển thị trạng thái đa luồng
        self.thread_status_label = QLabel("⚡ ĐA LUỒNG: ĐANG BẬT")
        self.thread_status_label.setStyleSheet("font-weight: bold; color: green; font-size: 12px; background-color: #E8F5E8; padding: 2px 8px; border-radius: 3px;")

        thread_layout.addWidget(thread_label)
        thread_layout.addWidget(self.thread_spinbox)
        thread_layout.addWidget(self.thread_status_label)
        thread_layout.addStretch()
        self.id_layout.addLayout(thread_layout)

        # Cài đặt số lượng follow/like mục tiêu
        target_layout = QHBoxLayout()
        target_label = QLabel("🎯 Số lượng follow:")
        self.target_count_spinbox = QSpinBox()
        self.target_count_spinbox.setMinimum(1)
        self.target_count_spinbox.setMaximum(999999)
        self.target_count_spinbox.setValue(100)
        self.target_count_spinbox.setToolTip("Số lượng tasks mục tiêu. Tool sẽ sử dụng đúng số lượng cookies này (1 cookie = 1 task) để đạt tốc độ tối ưu.")
        target_layout.addWidget(target_label)
        target_layout.addWidget(self.target_count_spinbox)
        target_layout.addStretch()
        self.id_layout.addLayout(target_layout)

        main_layout.addWidget(self.id_group)

        # Control buttons
        button_layout = QHBoxLayout()

        self.start_button = QPushButton("🚀 Bắt đầu Follow")
        self.start_button.clicked.connect(self.start_operation)
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)

        self.stop_button = QPushButton("⏹️ Dừng")
        self.stop_button.clicked.connect(self.stop_follow)
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)

        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.stop_button)
        main_layout.addLayout(button_layout)

        # Progress section
        progress_group = QGroupBox("📊 Tiến trình")
        progress_layout = QVBoxLayout(progress_group)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)

        self.progress_label = QLabel("Sẵn sàng...")
        progress_layout.addWidget(self.progress_label)

        main_layout.addWidget(progress_group)

        # Log section
        log_group = QGroupBox("📝 Nhật ký hoạt động")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        # Clear log button
        clear_log_button = QPushButton("🗑️ Xóa log")
        clear_log_button.clicked.connect(self.clear_log)
        log_layout.addWidget(clear_log_button)

        main_layout.addWidget(log_group)

        # Results section
        results_group = QGroupBox("📈 Kết quả")
        results_layout = QVBoxLayout(results_group)

        self.results_label = QLabel("Chưa có kết quả")
        self.results_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        results_font = QFont()
        results_font.setPointSize(12)
        results_font.setBold(True)
        self.results_label.setFont(results_font)
        results_layout.addWidget(self.results_label)

        main_layout.addWidget(results_group)

        # Log khởi động
        self.log_message("🚀 MIT Kling Follow Toolkit đã khởi động")
        self.log_message("✅ Chạy ở chế độ đầy đủ tính năng")
        self.log_message("📋 Vui lòng import cookie để bắt đầu")

    def on_operation_changed(self):
        """Xử lý khi thay đổi chế độ operation (Follow/Like)"""
        if self.follow_operation.isChecked():
            self.current_mode = "follow"
            self.id_group.setTitle("👥 Follow IDs")
            self.single_input.setPlaceholderText("Nhập Follow ID (ví dụ: 36709052)")
            self.multiple_input.setPlaceholderText("Nhập danh sách Follow IDs, mỗi ID một dòng:\n36709052\n12345678\n87654321")
            self.start_button.setText("🚀 Bắt đầu Follow")
        else:
            self.current_mode = "like"
            self.id_group.setTitle("❤️ Like IDs")
            self.single_input.setPlaceholderText("Nhập Like ID (ví dụ: video_id_123)")
            self.multiple_input.setPlaceholderText("Nhập danh sách Like IDs, mỗi ID một dòng:\nvideo_id_123\nvideo_id_456\nvideo_id_789")
            self.start_button.setText("❤️ Bắt đầu Like")

    def on_mode_changed(self):
        """Xử lý khi thay đổi chế độ nhập"""
        if self.single_mode.isChecked():
            self.single_input.show()
            self.multiple_input.hide()
            self.log_message("📍 Chuyển sang chế độ: Nhập 1 ID")
        else:
            self.single_input.hide()
            self.multiple_input.show()
            self.log_message("📋 Chuyển sang chế độ: Nhập nhiều IDs")

    def import_cookie(self):
        """Import tất cả cookies từ file .txt"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Chọn file cookie",
            "",
            "Text files (*.txt);;All files (*.*)"
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    lines = file.readlines()

                # Lọc các dòng không trống
                self.cookies = [line.strip() for line in lines if line.strip()]

                # Lưu đường dẫn file để tự động cập nhật sau này
                self.cookie_file_path = file_path

                if self.cookies:
                    self.cookie_status.setText(f"✓ Đã import {len(self.cookies)} cookie(s)")
                    self.cookie_status.setStyleSheet("color: green;")
                    self.log_message(f"Đã import {len(self.cookies)} cookie(s) thành công")
                    self.log_message(f"LIÊN HỆ 035-214-3210 MUA TOOL")

                    # Reset cookie stats
                    self.cookie_stats = None

                    # Hiển thị preview của cookie đầu tiên
                    if len(self.cookies[0]) > 50:
                        preview = self.cookies[0][:50] + "..."
                    else:
                        preview = self.cookies[0]
                    self.log_message(f"Cookie đầu tiên: {preview}")
                    self.log_message("💡 Cookies sẽ được tự động phân loại live/die trong quá trình follow!")
                else:
                    self.cookie_status.setText("✗ File cookie trống")
                    self.cookie_status.setStyleSheet("color: red;")
                    self.log_message("File cookie trống")

            except Exception as e:
                QMessageBox.critical(self, "Lỗi", f"Không thể đọc file cookie:\n{str(e)}")
                self.log_message(f"Lỗi đọc file cookie: {str(e)}")

    def toggle_auto_update(self):
        """Toggle tự động cập nhật cookie live"""
        self.auto_update_enabled = self.auto_update_checkbox.isChecked()

        if self.auto_update_enabled:
            self.auto_update_status.setText("✅ BẬT")
            self.auto_update_status.setStyleSheet("color: green; font-weight: bold; font-size: 11px;")
            self.auto_update_checkbox.setText("🔄 Tự động cập nhật cookie live")
            self.log_message("✅ Đã BẬT tự động cập nhật cookie live vào file đã import")
        else:
            self.auto_update_status.setText("❌ TẮT")
            self.auto_update_status.setStyleSheet("color: red; font-weight: bold; font-size: 11px;")
            self.auto_update_checkbox.setText("⏸️ Tự động cập nhật cookie live")
            self.log_message("❌ Đã TẮT tự động cập nhật cookie live vào file đã import")

    def auto_update_cookie_file(self):
        """Tự động cập nhật file cookie đã import với cookies live"""
        if not self.auto_update_enabled:
            return

        if not self.cookie_file_path:
            self.log_message("⚠️ Không có file cookie để cập nhật")
            return

        if not self.cookie_stats or not self.cookie_stats.get('live_cookies'):
            self.log_message("⚠️ Không có cookies live để cập nhật")
            return

        try:
            live_cookies = self.cookie_stats['live_cookies']

            # Ghi đè file cookie với danh sách cookies live
            with open(self.cookie_file_path, 'w', encoding='utf-8') as file:
                for cookie in live_cookies:
                    file.write(cookie + '\n')

            self.log_message(f"✅ Đã tự động cập nhật {len(live_cookies)} cookies live vào file: {self.cookie_file_path}")
            self.log_message(f"📁 File đã được ghi đè với {len(live_cookies)} cookies live")

        except Exception as e:
            self.log_message(f"❌ Lỗi khi tự động cập nhật file cookie: {str(e)}")

    def get_follow_ids(self):
        """Lấy danh sách follow IDs từ input"""
        follow_ids = []

        if self.single_mode.isChecked():
            # Chế độ single ID
            id_text = self.single_input.text().strip()
            if id_text:
                try:
                    follow_ids.append(str(int(id_text)))
                except ValueError:
                    QMessageBox.warning(self, "Cảnh báo", "Follow ID phải là số nguyên")
                    return []
        else:
            # Chế độ multiple IDs
            ids_text = self.multiple_input.toPlainText().strip()
            if ids_text:
                lines = ids_text.split('\n')
                for line in lines:
                    line = line.strip()
                    if line:
                        try:
                            follow_ids.append(str(int(line)))
                        except ValueError:
                            QMessageBox.warning(self, "Cảnh báo", f"ID không hợp lệ: {line}")
                            return []

        return follow_ids

    def get_like_ids(self):
        """Lấy danh sách like IDs từ input"""
        like_ids = []

        if self.single_mode.isChecked():
            # Chế độ single ID
            id_text = self.single_input.text().strip()
            if id_text:
                like_ids.append(str(id_text))
        else:
            # Chế độ multiple IDs
            ids_text = self.multiple_input.toPlainText().strip()
            if ids_text:
                lines = ids_text.split('\n')
                for line in lines:
                    line = line.strip()
                    if line:
                        like_ids.append(str(line))

        return like_ids

    def duplicate_id_for_all_cookies(self, target_id):
        """Duplicate 1 ID cho tất cả cookies"""
        if not self.cookies or not target_id:
            return []

        # Tạo danh sách với cùng 1 ID cho tất cả cookies
        num_cookies = len(self.cookies)
        duplicated_ids = [str(target_id)] * num_cookies

        return duplicated_ids

    def duplicate_id_for_all_cookies_live(self, target_id, cookies_list):
        """Duplicate 1 ID cho danh sách cookies được chỉ định"""
        if not cookies_list or not target_id:
            return []

        # Tạo danh sách với cùng 1 ID cho tất cả cookies trong list
        num_cookies = len(cookies_list)
        duplicated_ids = [str(target_id)] * num_cookies

        return duplicated_ids

    def distribute_ids_to_cookies(self, user_ids, cookies_list):
        """Phân phối danh sách IDs qua cookies - mỗi ID sẽ được follow bởi TẤT CẢ cookies"""
        if not user_ids or not cookies_list:
            return []

        # Tạo danh sách follow tasks: mỗi ID × tất cả cookies
        follow_ids = []
        for user_id in user_ids:
            # Mỗi ID sẽ được duplicate với tất cả cookies
            for _ in cookies_list:
                follow_ids.append(str(user_id))

        return follow_ids

    def distribute_ids_one_to_one(self, user_ids, cookies_list):
        """Phân phối danh sách IDs qua cookies - mỗi ID được follow bởi 1 cookie"""
        if not user_ids or not cookies_list:
            return []

        # Đơn giản: trả về tất cả IDs, FollowWorker sẽ tự phân phối qua cookies
        follow_ids = [str(user_id) for user_id in user_ids]

        return follow_ids

    def start_operation(self):
        """Bắt đầu quá trình follow hoặc like"""
        if self.current_mode == "follow":
            self.start_follow()
        else:
            self.start_like()

    def start_follow(self):
        """Bắt đầu quá trình follow"""
        # License check disabled - proceed directly

        # Kiểm tra cookies
        if not self.cookies:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng import cookie trước")
            return

        # Lấy số luồng và target count từ UI ngay từ đầu
        max_threads = self.thread_spinbox.value()
        target_count = self.target_count_spinbox.value()

        # Lấy danh sách follow IDs
        user_ids = self.get_follow_ids()
        if not user_ids:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng nhập ít nhất một Follow ID hợp lệ")
            return

        # Xử lý theo chế độ logic được chọn
        if self.speed_mode.isChecked():
            # LOGIC TỐI ƯU TỐC ĐỘ: 1 cookie = 1 task
            cookies_to_use = self.cookies[:target_count] if len(self.cookies) >= target_count else self.cookies
            follow_ids = user_ids  # Sử dụng IDs như nhập vào

            self.log_message(f"⚡ LOGIC TỐI ƯU TỐC ĐỘ: Target {target_count} tasks")
            self.log_message(f"📊 Sử dụng {len(cookies_to_use)} cookies đầu tiên (1 cookie = 1 task)")
            self.log_message(f"🔄 {len(user_ids)} IDs sẽ được phân phối round-robin qua cookies")

        else:
            # LOGIC TUẦN TỰ THEO ACCOUNT: Mỗi account được follow bởi TẤT CẢ cookies
            cookies_to_use = self.cookies[:target_count] if len(self.cookies) >= target_count else self.cookies

            self.log_message(f"� LOGIC HOÀN CHỈNH: Mỗi cookie follow hết tất cả accounts")
            self.log_message(f"� {len(cookies_to_use)} cookies × {len(user_ids)} accounts = {len(cookies_to_use) * len(user_ids)} tasks")
            self.log_message(f"🎯 Target: {target_count} cookies (không quan tâm live/die)")
            self.log_message(f"📋 Thứ tự: Account 1 → Cookie 1→{len(cookies_to_use)} → Account 2 → Cookie 1→{len(cookies_to_use)} → ...")

            # Hiển thị preview accounts
            if len(user_ids) <= 10:
                self.log_message(f"📋 Accounts: {', '.join(user_ids)}")
            else:
                preview_ids = user_ids[:5] + ['...'] + user_ids[-2:]
                self.log_message(f"📋 Accounts: {', '.join(preview_ids)} (tổng {len(user_ids)} accounts)")

            # Tạo follow_ids để tránh lỗi (không sử dụng trong CompleteFollowWorker)
            follow_ids = user_ids

        # Reset UI
        self.progress_bar.setValue(0)
        self.results_label.setText("Đang xử lý...")
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # Log bắt đầu với logic mới
        self.log_message(f"🚀 LOGIC MỚI - Tối ưu tốc độ: Target {target_count} tasks")
        self.log_message(f"📊 Sử dụng {min(target_count, len(cookies_to_use))} cookies đầu tiên (1 cookie = 1 task)")
        self.log_message(f"⚡ Không cần kiểm tra cookie live/die - chạy nhanh hơn!")
        if len(follow_ids) > 1:
            self.log_message(f"� {len(follow_ids)} IDs sẽ được phân phối round-robin qua cookies")

        # Cập nhật hiển thị trạng thái đa luồng
        self.thread_status_label.setText(f"⚡ ĐA LUỒNG: ĐANG CHẠY ({max_threads} luồng)")
        self.thread_status_label.setStyleSheet("font-weight: bold; color: orange; font-size: 12px; background-color: #FFF3E0; padding: 2px 8px; border-radius: 3px;")

        # Tạo và chạy worker thread
        if self.complete_mode.isChecked():
            # Sử dụng CompleteFollowWorker cho logic hoàn chỉnh
            self.follow_worker = CompleteFollowWorker(cookies_to_use, user_ids, max_threads)
            self.log_message(f"🔄 Khởi động LOGIC TUẦN TỰ với {max_threads} luồng song song")
        else:
            # Sử dụng FollowWorker cho logic tối ưu tốc độ
            self.follow_worker = FollowWorker(cookies_to_use, follow_ids, max_threads, target_count)
            self.log_message(f"⚡ Khởi động LOGIC TỐI ƯU với {max_threads} luồng song song")

        self.follow_worker.progress_updated.connect(self.update_progress)
        self.follow_worker.status_updated.connect(self.log_message)
        self.follow_worker.finished.connect(self.on_follow_finished)
        self.follow_worker.start()

    def start_like(self):
        """Bắt đầu quá trình like"""
        # License check disabled - proceed directly

        # Kiểm tra cookies
        if not self.cookies:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng import cookie trước")
            return

        # Lấy số luồng và target count từ UI ngay từ đầu
        max_threads = self.thread_spinbox.value()
        target_count = self.target_count_spinbox.value()

        # Lấy số luồng và target count từ UI ngay từ đầu
        max_threads = self.thread_spinbox.value()
        target_count = self.target_count_spinbox.value()

        # Lấy danh sách like IDs
        user_ids = self.get_like_ids()
        if not user_ids:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng nhập ít nhất một Like ID hợp lệ")
            return

        # Xử lý theo chế độ logic được chọn
        if self.speed_mode.isChecked():
            # LOGIC TỐI ƯU TỐC ĐỘ: 1 cookie = 1 task
            cookies_to_use = self.cookies[:target_count] if len(self.cookies) >= target_count else self.cookies
            like_ids = user_ids  # Sử dụng IDs như nhập vào

            self.log_message(f"⚡ LOGIC TỐI ƯU TỐC ĐỘ: Target {target_count} tasks")
            self.log_message(f"📊 Sử dụng {len(cookies_to_use)} cookies đầu tiên (1 cookie = 1 task)")
            self.log_message(f"🔄 {len(user_ids)} IDs sẽ được phân phối round-robin qua cookies")

        else:
            # LOGIC HOÀN CHỈNH: Mỗi cookie like hết tất cả videos
            cookies_to_use = self.cookies[:target_count] if len(self.cookies) >= target_count else self.cookies

            self.log_message(f"� LOGIC HOÀN CHỈNH: Mỗi cookie like hết tất cả videos")
            self.log_message(f"� {len(cookies_to_use)} cookies × {len(user_ids)} videos = {len(cookies_to_use) * len(user_ids)} tasks")
            self.log_message(f"🎯 Target: {target_count} cookies (không quan tâm live/die)")

            # Hiển thị preview videos
            if len(user_ids) <= 10:
                self.log_message(f"📋 Videos: {', '.join(user_ids)}")
            else:
                preview_ids = user_ids[:5] + ['...'] + user_ids[-2:]
                self.log_message(f"📋 Videos: {', '.join(preview_ids)} (tổng {len(user_ids)} videos)")

            # Tạo like_ids để tránh lỗi (không sử dụng trong CompleteLikeWorker)
            like_ids = user_ids

        # Reset UI
        self.progress_bar.setValue(0)
        self.results_label.setText("Đang xử lý...")
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # Cập nhật hiển thị trạng thái đa luồng
        self.thread_status_label.setText(f"⚡ ĐA LUỒNG: ĐANG CHẠY ({max_threads} luồng)")
        self.thread_status_label.setStyleSheet("font-weight: bold; color: orange; font-size: 12px; background-color: #FFF3E0; padding: 2px 8px; border-radius: 3px;")

        # Tạo và chạy worker thread
        if self.complete_mode.isChecked():
            # Sử dụng CompleteLikeWorker cho logic hoàn chỉnh
            self.like_worker = CompleteLikeWorker(cookies_to_use, user_ids, max_threads)
            self.log_message(f"🔄 Khởi động LOGIC TUẦN TỰ với {max_threads} luồng song song")
        else:
            # Sử dụng LikeWorker cho logic tối ưu tốc độ
            self.like_worker = LikeWorker(cookies_to_use, like_ids, max_threads, target_count)
            self.log_message(f"⚡ Khởi động LOGIC TỐI ƯU với {max_threads} luồng song song")

        self.like_worker.progress_updated.connect(self.update_progress)
        self.like_worker.status_updated.connect(self.log_message)
        self.like_worker.finished.connect(self.on_like_finished)
        self.like_worker.start()

    def stop_follow(self):
        """Dừng quá trình follow hoặc like"""
        if self.follow_worker and self.follow_worker.isRunning():
            self.follow_worker.stop()  # Dừng gracefully
            self.follow_worker.wait(3000)  # Đợi tối đa 3 giây
            if self.follow_worker.isRunning():
                self.follow_worker.terminate()  # Force terminate nếu cần
            self.log_message("🛑 Đã dừng quá trình follow")
            self.reset_ui()

        if self.like_worker and self.like_worker.isRunning():
            self.like_worker.stop()  # Dừng gracefully
            self.like_worker.wait(3000)  # Đợi tối đa 3 giây
            if self.like_worker.isRunning():
                self.like_worker.terminate()  # Force terminate nếu cần
            self.log_message("🛑 Đã dừng quá trình like")
            self.reset_ui()

    def update_progress(self, value):
        """Cập nhật progress bar"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(f"Tiến trình: {value}%")

    def log_message(self, message):
        """Thêm message vào log"""
        self.log_text.append(f"[{self.get_current_time()}] {message}")
        # Auto scroll to bottom
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def clear_log(self):
        """Xóa log"""
        self.log_text.clear()

    def on_follow_finished(self, success_count, total_count, cookie_stats):
        """Xử lý khi hoàn thành follow"""
        self.cookie_stats = cookie_stats
        self.reset_ui()

        # Hiển thị kết quả follow với logic mới
        success_rate = (success_count / total_count * 100) if total_count > 0 else 0
        result_text = f"✅ Hoàn thành!\n{total_count} tasks đã xử lý\n{success_count} thành công ({success_rate:.1f}%)"
        self.results_label.setText(result_text)

        # Hiển thị thống kê cookies
        live_count = len(cookie_stats['live_cookies'])
        die_count = len(cookie_stats['die_cookies'])
        unknown_count = len(cookie_stats['unknown_cookies'])

        self.log_message("=" * 50)
        self.log_message("📊 KẾT QUẢ HOÀN THÀNH:")
        self.log_message(f"✅ Tasks thành công: {success_count}/{total_count} ({success_rate:.1f}%)")
        self.log_message(f"⚡ Logic tối ưu: 1 cookie = 1 task (nhanh hơn)")
        self.log_message("📊 THỐNG KÊ COOKIES:")
        self.log_message(f"✅ Cookies LIVE: {live_count}")
        self.log_message(f"❌ Cookies DIE (HTTP 401): {die_count}")
        self.log_message(f"❓ Cookies không rõ: {unknown_count}")

        if live_count > 0:
            self.log_message(f"📈 Tỷ lệ cookies live: {(live_count/(live_count+die_count+unknown_count)*100):.1f}%")
            # Enable nút xuất cookies live
            self.export_live_button.setEnabled(True)
            self.log_message("💾 Có thể xuất cookies live bằng nút 'Xuất Cookies Live'")

        # Enable nút xuất kết quả
        self.export_results_button.setEnabled(True)
        self.log_message("📊 Có thể xuất báo cáo kết quả bằng nút 'Xuất Kết Quả'")

        self.log_message("=" * 50)

        if success_rate == 100:
            self.results_label.setStyleSheet("color: green;")
        elif success_rate > 50:
            self.results_label.setStyleSheet("color: orange;")
        else:
            self.results_label.setStyleSheet("color: red;")

        # Log kết quả
        self.log_message(f"Hoàn thành! {success_count}/{total_count} follow thành công")

        # Tự động cập nhật file cookie nếu được bật
        self.auto_update_cookie_file()

        # Hiển thị thông báo
        QMessageBox.information(
            self,
            "Hoàn thành",
            f"Đã hoàn thành quá trình follow!\n\n"
            f"Thành công: {success_count}/{total_count}\n"
            f"Tỷ lệ thành công: {success_rate:.1f}%"
        )

    def on_like_finished(self, success_count, total_count, cookie_stats):
        """Xử lý khi hoàn thành like"""
        self.cookie_stats = cookie_stats
        self.reset_ui()

        # Hiển thị kết quả like với logic mới
        success_rate = (success_count / total_count * 100) if total_count > 0 else 0
        result_text = f"✅ Hoàn thành!\n{total_count} tasks đã xử lý\n{success_count} thành công ({success_rate:.1f}%)"
        self.results_label.setText(result_text)

        # Hiển thị thống kê cookies
        live_count = len(cookie_stats['live_cookies'])
        die_count = len(cookie_stats['die_cookies'])
        unknown_count = len(cookie_stats['unknown_cookies'])

        self.log_message("=" * 50)
        self.log_message("📊 KẾT QUẢ HOÀN THÀNH:")
        self.log_message(f"✅ Tasks thành công: {success_count}/{total_count} ({success_rate:.1f}%)")
        self.log_message(f"⚡ Logic tối ưu: 1 cookie = 1 task (nhanh hơn)")
        self.log_message("📊 THỐNG KÊ COOKIES:")
        self.log_message(f"✅ Cookies LIVE: {live_count}")
        self.log_message(f"❌ Cookies DIE (HTTP 401): {die_count}")
        self.log_message(f"❓ Cookies không rõ: {unknown_count}")

        if live_count > 0:
            self.log_message(f"📈 Tỷ lệ cookies live: {(live_count/(live_count+die_count+unknown_count)*100):.1f}%")
            # Enable nút xuất cookies live
            self.export_live_button.setEnabled(True)
            self.log_message("💾 Có thể xuất cookies live bằng nút 'Xuất Cookies Live'")

        # Enable nút xuất kết quả
        self.export_results_button.setEnabled(True)
        self.log_message("📊 Có thể xuất báo cáo kết quả bằng nút 'Xuất Kết Quả'")

        self.log_message("=" * 50)

        if success_rate == 100:
            self.results_label.setStyleSheet("color: green;")
        elif success_rate > 50:
            self.results_label.setStyleSheet("color: orange;")
        else:
            self.results_label.setStyleSheet("color: red;")

        # Log kết quả
        self.log_message(f"Hoàn thành! {success_count}/{total_count} like thành công")

        # Tự động cập nhật file cookie nếu được bật
        self.auto_update_cookie_file()

        # Hiển thị thông báo
        QMessageBox.information(
            self,
            "Hoàn thành",
            f"Đã hoàn thành quá trình like!\n\n"
            f"Thành công: {success_count}/{total_count}\n"
            f"Tỷ lệ thành công: {success_rate:.1f}%"
        )

    def export_live_cookies(self):
        """Xuất cookies live ra file"""
        if not self.cookie_stats or not self.cookie_stats['live_cookies']:
            QMessageBox.warning(self, "Lỗi", "Không có cookies live để xuất!\nVui lòng follow trước để phân loại cookies.")
            return

        live_cookies = self.cookie_stats['live_cookies']

        # Chọn đường dẫn lưu file
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Lưu cookies live",
            f"cookies_live_{self.get_current_time().replace(':', '')}.txt",
            "Text files (*.txt);;All files (*.*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as file:
                    for cookie in live_cookies:
                        file.write(cookie + '\n')

                self.log_message(f"✅ Đã xuất {len(live_cookies)} cookies live ra file: {file_path}")

                # Hiển thị thông báo thành công với thống kê chi tiết
                die_count = len(self.cookie_stats['die_cookies'])
                unknown_count = len(self.cookie_stats['unknown_cookies'])

                QMessageBox.information(
                    self,
                    "Xuất thành công",
                    f"✅ Đã xuất {len(live_cookies)} cookies live ra file:\n\n"
                    f"{file_path}\n\n"
                    f"📊 Thống kê:\n"
                    f"✅ Live: {len(live_cookies)}\n"
                    f"❌ Die (HTTP 401): {die_count}\n"
                    f"❓ Không rõ: {unknown_count}\n\n"
                    f"💡 Bạn có thể sử dụng file này để import cookies live!"
                )

                # Hỏi có muốn import file vừa xuất không
                reply = QMessageBox.question(
                    self,
                    "Import cookies live",
                    "Bạn có muốn import file cookies live vừa xuất để sử dụng ngay không?",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.Yes
                )

                if reply == QMessageBox.StandardButton.Yes:
                    self.import_live_cookies_file(file_path)

            except Exception as e:
                QMessageBox.critical(self, "Lỗi", f"Không thể xuất file cookies:\n{str(e)}")
                self.log_message(f"❌ Lỗi xuất file cookies: {str(e)}")

    def import_live_cookies_file(self, file_path):
        """Import file cookies live vừa xuất"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()

            # Lọc các dòng không trống
            live_cookies = [line.strip() for line in lines if line.strip()]

            if live_cookies:
                # Cập nhật cookies chính bằng cookies live
                self.cookies = live_cookies

                self.cookie_status.setText(f"✅ Đã import {len(live_cookies)} cookies live")
                self.cookie_status.setStyleSheet("color: green;")
                self.log_message(f"✅ Đã import {len(live_cookies)} cookies live từ file xuất")
                self.log_message("🎯 Sẵn sàng follow với cookies đã được kiểm tra!")

                # Reset cookie stats vì đã import cookies mới
                self.cookie_stats = None
                self.export_live_button.setEnabled(False)

        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể import file cookies live:\n{str(e)}")
            self.log_message(f"❌ Lỗi import file cookies live: {str(e)}")



    def reset_ui(self):
        """Reset UI về trạng thái ban đầu"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setValue(0)
        self.progress_label.setText("Sẵn sàng...")

        # Reset trạng thái đa luồng
        if hasattr(self, 'thread_status_label'):
            self.thread_status_label.setText("⚡ ĐA LUỒNG: ĐANG BẬT")
            self.thread_status_label.setStyleSheet("font-weight: bold; color: green; font-size: 12px; background-color: #E8F5E8; padding: 2px 8px; border-radius: 3px;")

    def export_results(self):
        """Xuất báo cáo kết quả chi tiết"""
        if not self.cookie_stats:
            QMessageBox.warning(self, "Cảnh báo", "Chưa có dữ liệu để xuất")
            return

        try:
            # Chọn file để lưu
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Xuất báo cáo kết quả",
                f"kling_results_{self.get_current_time().replace(':', '-').replace(' ', '_')}.txt",
                "Text files (*.txt);;All files (*.*)"
            )

            if not file_path:
                return

            # Tạo báo cáo chi tiết
            report_lines = []
            report_lines.append("=" * 80)
            report_lines.append("📊 BÁO CÁO KẾT QUẢ KLING AI TOOLKIT")
            report_lines.append("=" * 80)
            report_lines.append(f"⏰ Thời gian: {self.get_current_time()}")
            report_lines.append(f"🎯 Chế độ: {self.current_mode.upper()}")
            report_lines.append("")

            # Thống kê tổng quan
            live_count = len(self.cookie_stats['live_cookies'])
            die_count = len(self.cookie_stats['die_cookies'])
            unknown_count = len(self.cookie_stats['unknown_cookies'])
            total_cookies = live_count + die_count + unknown_count

            report_lines.append("📈 THỐNG KÊ TỔNG QUAN:")
            report_lines.append("-" * 40)
            report_lines.append(f"✅ Cookies LIVE: {live_count}")
            report_lines.append(f"❌ Cookies DIE: {die_count}")
            report_lines.append(f"❓ Cookies không rõ: {unknown_count}")
            report_lines.append(f"📊 Tổng cookies: {total_cookies}")
            if total_cookies > 0:
                report_lines.append(f"📈 Tỷ lệ live: {(live_count/total_cookies*100):.1f}%")
            report_lines.append("")

            # Chi tiết từng cookie
            report_lines.append("📋 CHI TIẾT TỪNG COOKIE:")
            report_lines.append("-" * 40)

            for cookie, stats in self.cookie_stats['cookie_results'].items():
                status_icon = "✅" if stats['status'] == 'live' else "❌" if stats['status'] == 'die' else "❓"
                success_rate = (stats['success'] / stats['attempts'] * 100) if stats['attempts'] > 0 else 0

                report_lines.append(f"{status_icon} Cookie #{stats['cookie_num']}:")
                report_lines.append(f"   📊 Attempts: {stats['attempts']}")
                report_lines.append(f"   ✅ Success: {stats['success']}")
                report_lines.append(f"   📈 Success Rate: {success_rate:.1f}%")
                report_lines.append(f"   🔗 Cookie: {cookie[:50]}...")
                report_lines.append("")

            # Cookies live (để copy)
            if live_count > 0:
                report_lines.append("💾 COOKIES LIVE (SẴN SÀNG SỬ DỤNG):")
                report_lines.append("-" * 40)
                for i, cookie in enumerate(self.cookie_stats['live_cookies'], 1):
                    report_lines.append(f"{i}. {cookie}")
                report_lines.append("")

            # Ghi file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report_lines))

            # Thông báo thành công
            QMessageBox.information(
                self,
                "Thành công",
                f"Đã xuất báo cáo kết quả vào file:\n{file_path}\n\n"
                f"📊 Tổng cookies: {total_cookies}\n"
                f"✅ Live: {live_count}\n"
                f"❌ Die: {die_count}"
            )

        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể xuất báo cáo:\n{str(e)}")

    @staticmethod
    def get_current_time():
        """Lấy thời gian hiện tại"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")


def main():
    """Hàm main để chạy ứng dụng"""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("Kling AI Follow Toolkit")
    app.setApplicationVersion("1.0")

    # Create and show main window
    window = KlingFollowGUI()
    window.show()

    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()