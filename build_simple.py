#!/usr/bin/env python3
"""
MIT Kling Follow - Simple Build Script
"""

import os
import subprocess
import sys

def build_exe():
    """Build executable with PyInstaller"""
    print("🚀 Building MIT Kling Follow...")
    
    # Simple PyInstaller command
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=MIT_Kling_Follow",
        "--add-data=license_api.py;.",
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=requests",
        "--hidden-import=urllib3",
        "--hidden-import=certifi",
        "kling_follow_gui.py"
    ]
    
    try:
        print("📦 Running PyInstaller...")
        result = subprocess.run(cmd, check=True)
        print("✅ Build successful!")
        print("📁 Output: dist/MIT_Kling_Follow.exe")
        
        # Check file size
        exe_path = "dist/MIT_Kling_Follow.exe"
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📏 Size: {size_mb:.1f} MB")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        return False

def main():
    print("=" * 50)
    print("🚀 MIT KLING FOLLOW - BUILD")
    print("=" * 50)
    
    if build_exe():
        print("\n✅ Build completed!")
        print("🎯 Machine ID based license system ready")
        print("📱 Contact: ********** for licenses")
    else:
        print("\n❌ Build failed!")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
