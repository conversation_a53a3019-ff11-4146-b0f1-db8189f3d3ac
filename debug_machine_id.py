#!/usr/bin/env python3
"""
Debug Machine ID Script
"""

import hashlib
import subprocess
import platform
from license_api import LicenseAPI

def debug_machine_id():
    """Debug machine ID generation"""
    print("🔍 DEBUGGING MACHINE ID GENERATION")
    print("=" * 50)
    
    system = platform.system()
    print(f"🖥️ System: {system}")
    print(f"🏷️ Node: {platform.node()}")
    print(f"🔧 Machine: {platform.machine()}")
    print(f"⚙️ Processor: {platform.processor()}")
    print()
    
    if system == "Windows":
        print("🪟 Testing Windows methods:")
        methods = [
            (['wmic', 'csproduct', 'get', 'uuid'], "UUID"),
            (['wmic', 'bios', 'get', 'serialnumber'], "BIOS Serial"),
            (['wmic', 'baseboard', 'get', 'serialnumber'], "Baseboard Serial")
        ]
        
        for method, name in methods:
            try:
                print(f"  🔍 Testing {name}...")
                result = subprocess.run(method, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    print(f"    Raw output: {lines}")
                    for line in lines:
                        line = line.strip()
                        if line and not line.lower().startswith(('uuid', 'serialnumber')):
                            print(f"    ✅ Found: {line}")
                            break
                else:
                    print(f"    ❌ Failed: {result.stderr}")
            except Exception as e:
                print(f"    ❌ Error: {e}")
            print()
    
    # Test fallback method
    print("🔄 Testing fallback method:")
    system_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
    fallback_id = hashlib.md5(system_info.encode()).hexdigest()[:12].upper()
    print(f"  System info: {system_info}")
    print(f"  Fallback ID: {fallback_id}")
    print()
    
    # Test LicenseAPI
    print("🔐 Testing LicenseAPI:")
    try:
        license_api = LicenseAPI("http://*************:5001", "mit_kling_follow")
        print(f"  Raw Machine ID: {license_api.machine_id}")
        print(f"  Formatted ID: {license_api.formatted_machine_id}")
        print(f"  License Key: {license_api.license_key}")
    except Exception as e:
        print(f"  ❌ Error: {e}")

if __name__ == "__main__":
    debug_machine_id()
